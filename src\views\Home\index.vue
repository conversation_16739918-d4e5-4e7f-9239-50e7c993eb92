<template>
  <div class="home-page">
    <!-- 主要内容 -->
    <main class="page-content">
      <!-- 轮播图 -->
      <section class="hero-slider">
        <div class="slider-container">
          <div class="slider-wrapper" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
            <div class="slide" v-for="(slide, index) in slides" :key="index">
              <div class="slide-content" :style="{ backgroundImage: `url(${slide.background})` }">
                <div class="slide-text">
                  <img :src="slide.logo" :alt="slide.title" />
                  <p>{{ slide.description }}</p>
                  <button class="learn-more-btn" @click="learnMore(slide)">瞭解更多 >></button>
                </div>
              </div>
            </div>
          </div>
          <button class="slider-btn prev-btn" @click="prevSlide">‹</button>
          <button class="slider-btn next-btn" @click="nextSlide">›</button>
          <div class="slider-dots">
            <span
              v-for="(slide, index) in slides"
              :key="index"
              class="dot"
              :class="{ active: index === currentSlide }"
              @click="goToSlide(index)"
            ></span>
          </div>
        </div>
      </section>

      <!-- 智慧生态模块 -->
      <section class="smart-ecology">
        <div class="container">
          <div class="section-header">
            <h2>智慧生態</h2>
            <p class="section-subtitle">運用先進技術構建智慧生態監測與管理體系</p>
          </div>
          <div class="ecology-grid">
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/monitoring')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/073346750cbf5e05e3ea59df9549578a.png" alt="生態監測" />
              </div>
              <h3>生態監測</h3>
              <p>實時監測生態環境變化，提供科學數據支撐</p>
            </div>
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/warning')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/d1fba75503642fc884e617bc2a146ff9.png" alt="災害預警" />
              </div>
              <h3>災害預警</h3>
              <p>智能預警系統，及時發現潛在風險</p>
            </div>
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/fire-prevention')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png" alt="森林防火" />
              </div>
              <h3>森林防火</h3>
              <p>全方位森林火災防控解決方案</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 人工智能模块 -->
      <section class="ai-section">
        <div class="container">
          <div class="section-header">
            <h2>人工智能</h2>
            <p class="section-subtitle">領先的AI技術與解決方案</p>
          </div>
          <div class="ai-content">
            <div class="ai-category">
              <h3>垂類大模型</h3>
              <div class="ai-grid">
                <div class="ai-item" @click="navigateTo('/ai/multimodal')">
                  <img src="/uploads/sites/1012/2024/07/83841dc18fcc7f97315235a2ac571d46.png" alt="多模態融合" />
                  <span>多模態融合</span>
                  <p>整合文本、圖像、語音等多種數據模態</p>
                </div>
                <div class="ai-item" @click="navigateTo('/ai/training')">
                  <img src="/uploads/sites/1012/2024/07/34cf3b25d4852b8410937873b5be53f8.png" alt="大模型訓練" />
                  <span>大模型訓練</span>
                  <p>專業的大規模模型訓練服務</p>
                </div>
                <div class="ai-item" @click="navigateTo('/ai/nlp')">
                  <img src="/uploads/sites/1012/2024/07/b6ac1fea464d47ee66ddcc796b92268e.png" alt="自然語言處理" />
                  <span>自然語言處理</span>
                  <p>先進的NLP技術與應用</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 数据统计模块 -->
      <section class="statistics-section">
        <div class="container">
          <div class="statistics-grid">
            <div class="stat-card">
              <div class="stat-number">
                <span class="counter" data-target="24">24</span>
                <span class="plus">+</span>
              </div>
              <h3>行業理解</h3>
              <p>創新構建"AI 大腦+PaaS 引擎+SaaS 產品"的"雲智鏈一體"行業解決方案及全棧技術能力。</p>
            </div>
            <div class="stat-card">
              <div class="stat-number">
                <span class="counter" data-target="6">6</span>
                <span class="plus">+</span>
              </div>
              <h3>6 大技術支持落地</h3>
              <p>廣泛佈局前沿領域，加速推動 AI 在各垂直場景中的應用落地，為行業賦能。</p>
            </div>
            <div class="stat-card">
              <div class="stat-number">
                <span class="counter" data-target="3">3</span>
                <span class="plus">+X</span>
              </div>
              <h3>場景落地</h3>
              <p>以先發行業智慧文旅為起點，有序擴展智慧生態和城市服務領域。</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 新闻中心 -->
      <section class="news-center">
        <div class="container">
          <div class="section-header">
            <p class="section-subtitle">NEWS CENTER</p>
            <h2>新聞中心</h2>
          </div>
          <div class="news-list">
            <div class="news-item">
              <div class="news-image">
                <img src="/uploads/sites/1012/2024/08/c4a51cfaac61c49721e8aac314b36b58.png" alt="新闻图片" />
              </div>
              <div class="news-content">
                <h3>行業研究 I 我國生成式人工智能拐點探究</h3>
              </div>
            </div>
          </div>
          <button class="view-more-btn">查看更多 >></button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'

// 响应式数据
const currentSlide = ref(0)
const autoPlayTimer = ref(null)
const router = useRouter()

const slides = ref([
  {
    background: '/uploads/sites/1012/2022/11/a28adbebd944acf1203439567578dc64.jpg',
    logo: '/uploads/sites/1012/2024/12/49894a4535b5c9c8e99b1e377f624758.png',
    title: '數字起源',
    description: '以數字科技為基礎、以科技創新賦能美好生活',
    link: '/about'
  },
  {
    background: '/uploads/sites/1012/2022/11/文旅.jpg',
    logo: '/uploads/sites/1012/2022/12/8c2ec5739cfdd126fc68f6470b455444.png',
    title: '智慧文旅',
    description: '首創基於聯盟鏈應用的全場景目的地雲服務解決方案，以"智旅鏈 + 慧旅雲"雙擎驅動旅遊目的地數字化進程。',
    link: '/smart-culturetourism'
  },
  {
    background: '/uploads/sites/1012/2022/11/45e2a64eccb2ef4ce950126223ccabc9.jpg',
    logo: '/uploads/sites/1012/2024/12/智慧生态.png',
    title: '智慧生態',
    description: '運用先進的物聯網、大數據、人工智能等技術，構建智慧生態監測與管理體系。',
    link: '/smart-ecology'
  }
])

// 方法
const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % slides.value.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? slides.value.length - 1 : currentSlide.value - 1
}

const goToSlide = (index) => {
  currentSlide.value = index
}

const startAutoPlay = () => {
  autoPlayTimer.value = setInterval(() => {
    nextSlide()
  }, 5000)
}

const stopAutoPlay = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = null
  }
}

const learnMore = (slide) => {
  router.push(slide.link)
}

const navigateTo = (path) => {
  router.push(path)
}

const initCounters = () => {
  // 数字计数动画
  const animateCounter = (counter) => {
    const target = parseInt(counter.getAttribute('data-target'))
    const increment = target / 100
    let current = 0

    const updateCounter = () => {
      if (current < target) {
        current += increment
        counter.textContent = Math.ceil(current)
        requestAnimationFrame(updateCounter)
      } else {
        counter.textContent = target
      }
    }

    updateCounter()
  }

  // 使用 Intersection Observer 来触发动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const counter = entry.target.querySelector('.counter')
        if (counter && !counter.classList.contains('animated')) {
          counter.classList.add('animated')
          animateCounter(counter)
        }
      }
    })
  })

  document.querySelectorAll('.stat-card').forEach(card => {
    observer.observe(card)
  })
}

// 生命周期钩子
onMounted(() => {
  startAutoPlay()
  initCounters()
})

onBeforeUnmount(() => {
  stopAutoPlay()
})
</script>

<style scoped>
/* 基础样式将在单独的样式文件中定义 */
@import './styles.css';
</style>
