/* Home 页面样式 */
.home-page {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}



/* 轮播图样式 */
.hero-slider {
  position: relative;
  height: 600px;
  overflow: hidden;
}

.slider-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.slider-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.slide-content {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  position: relative;
}

.slide-text {
  max-width: 500px;
  padding: 0 80px;
  color: #fff;
}

.slide-text img {
  max-width: 200px;
  margin-bottom: 30px;
}

.slide-text p {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 30px;
}

.learn-more-btn {
  padding: 12px 24px;
  background: transparent;
  color: #fff;
  border: 1px solid #fff;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.learn-more-btn:hover {
  background: #fff;
  color: #333;
}

/* 轮播图控制按钮 */
.slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 10;
}

.slider-btn:hover {
  background: rgba(255, 255, 255, 0.5);
}

.prev-btn {
  left: 30px;
}

.next-btn {
  right: 30px;
}

/* 轮播图指示点 */
.slider-dots {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s;
}

.dot.active {
  background: #fff;
}

/* 智慧生态模块 */
.smart-ecology {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  color: #fff;
  font-size: 32px;
  margin: 0 0 10px 0;
}

.section-header .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.ecology-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.ecology-item {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ecology-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.item-icon {
  margin-bottom: 20px;
}

.item-icon img {
  width: 60px;
  height: 60px;
}

.ecology-item h3 {
  color: #fff;
  font-size: 18px;
  margin: 0 0 10px 0;
}

.ecology-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* 人工智能模块 */
.ai-section {
  padding: 80px 0;
  background: rgba(0, 100, 216, 0.31);
}

.ai-section .section-header h2 {
  color: #fff;
}

.ai-category h3 {
  color: #fff;
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.ai-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 600px;
  margin: 0 auto;
}

.ai-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ai-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.ai-item img {
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

.ai-item span {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}

.ai-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

/* 数据统计模块 */
.statistics-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.stat-card {
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png') no-repeat;
  background-size: contain;
  opacity: 0.1;
}

.stat-number {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}

.counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.plus {
  font-size: 24px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: bold;
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* 新闻中心 */
.news-center {
  padding: 80px 0;
  background: #fff;
}

.section-subtitle {
  color: #333;
  font-size: 14px;
  text-align: center;
  margin: 0 0 10px 0;
}

.news-center .section-header h2 {
  color: #333;
  text-align: center;
}

.news-list {
  max-width: 800px;
  margin: 0 auto 40px;
}

.news-item {
  display: flex;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.news-item:hover {
  background: rgba(245, 246, 251, 1);
  transform: translateY(-2px);
}

.news-image {
  width: 300px;
  height: 225px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  flex: 1;
  padding: 30px;
  display: flex;
  align-items: center;
}

.news-content h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.view-more-btn {
  display: block;
  margin: 0 auto;
  padding: 12px 24px;
  background: transparent;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  color: #0064d8;
  border-color: #0064d8;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-hover:hover {
  animation: pulse 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .slide-text {
    padding: 0 30px;
  }

  .ecology-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ai-grid {
    grid-template-columns: 1fr;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 200px;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stat-card {
    padding: 30px 20px;
  }

  .counter {
    font-size: 48px;
  }
}
